import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/theme/app_style_manager.dart';
import '../shared/widgets/loading/app_loading_components.dart';

/// Examples of how to use the centralized styling system
/// This file demonstrates the ONE PLACE approach to styling
class StyleUsageExamples extends ConsumerWidget {
  const StyleUsageExamples({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Style Usage Examples'),
        // AppBar automatically uses global theme configuration
      ),
      body: SingleChildScrollView(
        padding: AppStyleManager.standardPadding, // ✅ Global padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ==================== BUTTONS ====================
            _buildSection(
              context,
              'Buttons',
              [
                // ✅ Primary button with global styling
                ElevatedButton(
                  onPressed: () {},
                  style: AppStyleManager.primaryButton(context),
                  child: const Text('Primary Button'),
                ),
                const SizedBox(height: 12),
                
                // ✅ Secondary button with global styling
                OutlinedButton(
                  onPressed: () {},
                  style: AppStyleManager.secondaryButton(context),
                  child: const Text('Secondary Button'),
                ),
                const SizedBox(height: 12),
                
                // ✅ Danger button with global styling
                ElevatedButton(
                  onPressed: () {},
                  style: AppStyleManager.dangerButton(context),
                  child: const Text('Delete Workout'),
                ),
                const SizedBox(height: 12),
                
                // ✅ Text button with global styling
                TextButton(
                  onPressed: () {},
                  style: AppStyleManager.textButton(context),
                  child: const Text('Skip'),
                ),
              ],
            ),

            // ==================== INPUTS ====================
            _buildSection(
              context,
              'Input Fields',
              [
                // ✅ Text field with global styling
                TextField(
                  decoration: AppStyleManager.inputDecoration(
                    context,
                    labelText: 'Workout Name',
                    hintText: 'Enter workout name',
                    prefixIcon: const Icon(Icons.fitness_center),
                  ),
                ),
                const SizedBox(height: 16),
                
                // ✅ Error state input
                TextField(
                  decoration: AppStyleManager.inputDecoration(
                    context,
                    labelText: 'Weight (kg)',
                    hintText: 'Enter weight',
                    prefixIcon: const Icon(Icons.monitor_weight),
                    isError: true,
                  ),
                ),
              ],
            ),

            // ==================== CARDS ====================
            _buildSection(
              context,
              'Cards',
              [
                // ✅ Standard card with global styling
                Container(
                  decoration: AppStyleManager.cardDecoration(context),
                  padding: AppStyleManager.standardPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Standard Card',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppStyleManager.textPrimary(context),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'This card uses global styling that adapts to light/dark themes and iOS/Android platforms.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppStyleManager.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                
                // ✅ Elevated card with global styling
                Container(
                  decoration: AppStyleManager.elevatedCardDecoration(context),
                  padding: AppStyleManager.standardPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Elevated Card',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppStyleManager.textPrimary(context),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'This card has more elevation for important content.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppStyleManager.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                
                // ✅ Workout-specific card styling
                Container(
                  decoration: AppStyleManager.workoutCardDecoration(context),
                  padding: AppStyleManager.standardPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Workout Card',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppStyleManager.textPrimary(context),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Special styling for workout cards with gradient background.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppStyleManager.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // ==================== LOADING COMPONENTS ====================
            _buildSection(
              context,
              'Loading Components',
              [
                // ✅ Primary loader
                Center(
                  child: AppStyleManager.primaryLoader(
                    message: 'Loading workout...',
                  ),
                ),
                const SizedBox(height: 24),
                
                // ✅ Workout card skeleton
                AppStyleManager.workoutCardSkeleton(),
                const SizedBox(height: 16),
                
                // ✅ Exercise list skeleton
                AppStyleManager.exerciseListSkeleton(itemCount: 2),
              ],
            ),

            // ==================== SNACKBAR EXAMPLES ====================
            _buildSection(
              context,
              'Notifications',
              [
                // ✅ Success message button
                ElevatedButton(
                  onPressed: () {
                    AppStyleManager.showSuccess(
                      context,
                      'Workout completed successfully!',
                    );
                  },
                  style: AppStyleManager.primaryButton(context),
                  child: const Text('Show Success'),
                ),
                const SizedBox(height: 12),
                
                // ✅ Error message button
                ElevatedButton(
                  onPressed: () {
                    AppStyleManager.showError(
                      context,
                      'Failed to save workout',
                      action: () {
                        // Retry action
                      },
                      actionLabel: 'Retry',
                    );
                  },
                  style: AppStyleManager.dangerButton(context),
                  child: const Text('Show Error'),
                ),
                const SizedBox(height: 12),
                
                // ✅ Warning message button
                ElevatedButton(
                  onPressed: () {
                    AppStyleManager.showWarning(
                      context,
                      'Low battery detected during workout',
                    );
                  },
                  style: AppStyleManager.secondaryButton(context),
                  child: const Text('Show Warning'),
                ),
                const SizedBox(height: 12),
                
                // ✅ Info message button
                TextButton(
                  onPressed: () {
                    AppStyleManager.showInfo(
                      context,
                      'Tip: Take breaks between sets for better results',
                    );
                  },
                  style: AppStyleManager.textButton(context),
                  child: const Text('Show Info'),
                ),
              ],
            ),

            // ==================== PLATFORM & THEME INFO ====================
            _buildSection(
              context,
              'Platform & Theme Info',
              [
                Container(
                  decoration: AppStyleManager.cardDecoration(context),
                  padding: AppStyleManager.standardPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Configuration',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppStyleManager.textPrimary(context),
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow(context, 'Platform', AppStyleManager.isIOS ? 'iOS' : 'Android'),
                      _buildInfoRow(context, 'Theme', AppStyleManager.isDarkMode(context) ? 'Dark' : 'Light'),
                      _buildInfoRow(context, 'Animation Duration', '${AppStyleManager.mediumAnimation.inMilliseconds}ms'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppStyleManager.textPrimary(context),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...children,
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppStyleManager.textSecondary(context),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppStyleManager.textPrimary(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
