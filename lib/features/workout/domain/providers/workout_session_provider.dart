import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/supabase_service.dart';
import '../../../../core/utils/validation_utils.dart';
import '../../../../core/errors/app_exceptions.dart';
import '../models/workout_session.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';
import '../../../../shared/services/webhook_service.dart';
import '../models/voice_status.dart';

/// Workout session state
class WorkoutSessionState {
  final String? workoutLogId;
  final WorkoutSession? currentWorkout;
  final int currentExerciseIndex;
  final int currentSetIndex;
  final Duration elapsedTime;
  final WorkoutFlowStatus flowStatus;
  final bool isLoading;
  final String? error;
  final DateTime? startTime;
  final DateTime? lastSaveTime;
  final Map<String, dynamic> sessionData;

  // New properties for hands-free mode
  final int currentReps;
  final double currentWeight;
  final bool isResting;
  final int restDuration;
  final VoiceStatus voiceStatus;

  const WorkoutSessionState({
    this.workoutLogId,
    this.currentWorkout,
    this.currentExerciseIndex = 0,
    this.currentSetIndex = 0,
    this.elapsedTime = Duration.zero,
    this.flowStatus = WorkoutFlowStatus.ready,
    this.isLoading = false,
    this.error,
    this.startTime,
    this.lastSaveTime,
    this.sessionData = const {},
    this.currentReps = 0,
    this.currentWeight = 0.0,
    this.isResting = false,
    this.restDuration = 0,
    this.voiceStatus = VoiceStatus.disconnected,
  });

  WorkoutSessionState copyWith({
    String? workoutLogId,
    WorkoutSession? currentWorkout,
    int? currentExerciseIndex,
    int? currentSetIndex,
    Duration? elapsedTime,
    WorkoutFlowStatus? flowStatus,
    bool? isLoading,
    String? error,
    DateTime? startTime,
    DateTime? lastSaveTime,
    Map<String, dynamic>? sessionData,
    int? currentReps,
    double? currentWeight,
    bool? isResting,
    int? restDuration,
    VoiceStatus? voiceStatus,
  }) {
    return WorkoutSessionState(
      workoutLogId: workoutLogId ?? this.workoutLogId,
      currentWorkout: currentWorkout ?? this.currentWorkout,
      currentExerciseIndex: currentExerciseIndex ?? this.currentExerciseIndex,
      currentSetIndex: currentSetIndex ?? this.currentSetIndex,
      elapsedTime: elapsedTime ?? this.elapsedTime,
      flowStatus: flowStatus ?? this.flowStatus,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      startTime: startTime ?? this.startTime,
      lastSaveTime: lastSaveTime ?? this.lastSaveTime,
      sessionData: sessionData ?? this.sessionData,
      currentReps: currentReps ?? this.currentReps,
      currentWeight: currentWeight ?? this.currentWeight,
      isResting: isResting ?? this.isResting,
      restDuration: restDuration ?? this.restDuration,
      voiceStatus: voiceStatus ?? this.voiceStatus,
    );
  }
}

/// Workout flow status enum
enum WorkoutFlowStatus {
  ready,
  loading,
  preFlightChecks,
  countdown,
  active,
  resting,
  paused,
  completed,
  cancelled,
  error,
  saving,
}

/// Workout session notifier
class WorkoutSessionNotifier extends StateNotifier<WorkoutSessionState> {
  final Ref ref;

  WorkoutSessionNotifier(this.ref) : super(const WorkoutSessionState());

  Timer? _autoSaveTimer;
  Timer? _workoutTimer;

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _workoutTimer?.cancel();
    super.dispose();
  }

  /// Start the complete workout flow
  Future<void> startWorkoutFlow(TodayWorkout todayWorkout) async {
    try {
      if (!mounted) return;
      state = state.copyWith(
        isLoading: true,
        flowStatus: WorkoutFlowStatus.loading,
        error: null,
      );

      // Step 1: Pre-flight checks
      await _performPreFlightChecks(todayWorkout);

      // Step 2: Create workout log entry
      final workoutLogId = await _createWorkoutLog(todayWorkout.id);

      // Step 3: Check for incomplete sessions
      await _checkIncompleteSession();

      // Step 4: Convert TodayWorkout to WorkoutSession
      final workoutSession = await _convertToWorkoutSession(todayWorkout);

      // Step 5: Preload exercise data
      await _preloadExerciseData(workoutSession);

      if (!mounted) return;
      state = state.copyWith(
        workoutLogId: workoutLogId,
        currentWorkout: workoutSession,
        flowStatus: WorkoutFlowStatus.countdown,
        isLoading: false,
        startTime: DateTime.now(),
      );

      // Start auto-save timer
      _startAutoSave();

    } catch (e, s) {
      debugPrint('❌ ERROR in startWorkoutFlow: $e\nStackTrace: $s');
      if (!mounted) return;
      state = state.copyWith(
        isLoading: false,
        flowStatus: WorkoutFlowStatus.error,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Perform pre-flight checks
  Future<void> _performPreFlightChecks(TodayWorkout workout) async {
    if (!mounted) return;
    state = state.copyWith(flowStatus: WorkoutFlowStatus.preFlightChecks);

    // Check device storage space
    // Check network connectivity
    // Initialize motion/health tracking
    // Set screen brightness
    // Disable auto-lock

    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Create workout log entry in database
  Future<String> _createWorkoutLog(String workoutId) async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('🔄 Creating workout log for user: $userId, workout: $workoutId');

      // First, check for existing in-progress sessions and clean them up
      final existingSessions = await SupabaseService.client
          .from('workout_logs')
          .select('id')
          .eq('user_id', userId)
          .eq('status', 'in_progress');

      // Cancel any existing in-progress sessions
      if (existingSessions.isNotEmpty) {
        debugPrint('🔄 Cancelling ${existingSessions.length} existing in-progress sessions');
        await SupabaseService.client
            .from('workout_logs')
            .update({'status': 'cancelled'})
            .eq('user_id', userId)
            .eq('status', 'in_progress');
      }

      // Now create the new workout log
      final response = await SupabaseService.client
          .from('workout_logs')
          .insert({
            'user_id': userId,
            'workout_id': workoutId,
            'started_at': DateTime.now().toIso8601String(),
            'status': 'in_progress',
          })
          .select('id')
          .single();

      final logId = response['id'] as String;
      debugPrint('✅ Created workout log: $logId');
      return logId;

    } catch (e) {
      debugPrint('❌ Error creating workout log: $e');
      throw Exception('Failed to create workout log: ${e.toString()}');
    }
  }

  /// Check for incomplete sessions
  Future<void> _checkIncompleteSession() async {
    final userId = SupabaseService.currentUser?.id;
    if (userId == null) return;

    final incompleteSession = await SupabaseService.client
        .from('workout_logs')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'in_progress')
        .maybeSingle();

    if (incompleteSession != null) {
      // Handle resume scenario
      // For now, we'll continue with new session
      debugPrint('Found incomplete session: ${incompleteSession['id']}');
    }
  }

  /// Convert TodayWorkout to WorkoutSession
  Future<WorkoutSession> _convertToWorkoutSession(TodayWorkout todayWorkout) async {
    // Convert TodayWorkoutExercise to WorkoutExercise
    final convertedExercises = todayWorkout.exercises.map((todayExercise) {
      return WorkoutExercise(
        id: todayExercise.id,
        name: todayExercise.name,
        description: todayExercise.description,
        sets: todayExercise.sets,
        targetReps: todayExercise.reps, // Convert reps to targetReps
        targetWeights: todayExercise.weights, // Convert weights to targetWeights
        videoUrl: todayExercise.videoUrl,
        thumbnailUrl: todayExercise.thumbnailUrl,
        instructions: todayExercise.instructions,
        restInterval: todayExercise.restInterval,
        isCompleted: todayExercise.isCompleted,
      );
    }).toList();

    return WorkoutSession(
      id: todayWorkout.id,
      name: todayWorkout.name,
      description: todayWorkout.description,
      estimatedDuration: todayWorkout.estimatedDuration,
      estimatedCalories: todayWorkout.estimatedCalories,
      totalSets: todayWorkout.totalSets,
      exercises: convertedExercises,
      status: WorkoutStatus.ready,
    );
  }

  /// Preload exercise data (videos, images)
  Future<void> _preloadExerciseData(WorkoutSession workout) async {
    // Preload first 3 exercise videos
    final exercisesToPreload = workout.exercises.take(3);
    
    for (final exercise in exercisesToPreload) {
      if (exercise.videoUrl != null) {
        // Preload video
        debugPrint('Preloading video for: ${exercise.name}');
      }
    }
  }

  /// Start auto-save timer
  void _startAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _saveWorkoutState();
    });
  }

  /// Save workout state to database
  Future<void> _saveWorkoutState() async {
    if (state.workoutLogId == null) return;

    try {
      // Update using only existing columns in workout_logs table
      await SupabaseService.client
          .from('workout_logs')
          .update({
            'duration_seconds': state.elapsedTime.inSeconds,
            'notes': 'Session data: ${jsonEncode({
              'current_exercise_index': state.currentExerciseIndex,
              'current_set_index': state.currentSetIndex,
              'session_data': state.sessionData,
              'last_updated': DateTime.now().toIso8601String(),
            })}',
          })
          .eq('id', state.workoutLogId!);

      if (!mounted) return;
      state = state.copyWith(lastSaveTime: DateTime.now());
      debugPrint('📝 Workout state saved successfully to database');
    } catch (e) {
      debugPrint('❌ Failed to save workout state: $e');
    }
  }

  /// Start active workout
  void startActiveWorkout() {
    if (!mounted) return;
    
    debugPrint('🚀 Starting active workout...');
    debugPrint('  Before: currentExerciseIndex=${state.currentExerciseIndex}, currentSetIndex=${state.currentSetIndex}');
    
    state = state.copyWith(
      flowStatus: WorkoutFlowStatus.active,
      startTime: DateTime.now(),
      currentExerciseIndex: 0,  // Ensure we start from the first exercise
      currentSetIndex: 0,       // Ensure we start from the first set
    );
    
    debugPrint('  After: currentExerciseIndex=${state.currentExerciseIndex}, currentSetIndex=${state.currentSetIndex}');
    debugPrint('✅ Active workout started successfully');

    // Sync the set index with completed sets to ensure consistency
    _syncCurrentSetIndex();

    _startWorkoutTimer();
  }

  /// Start workout timer
  void _startWorkoutTimer() {
    _workoutTimer?.cancel();
    _workoutTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (state.flowStatus == WorkoutFlowStatus.active) {
        if (!mounted) return;
        state = state.copyWith(
          elapsedTime: state.elapsedTime + const Duration(seconds: 1),
        );
      }
    });
  }

  /// Pause workout
  void pauseWorkout() {
    if (!mounted) return;
    state = state.copyWith(flowStatus: WorkoutFlowStatus.paused);
    _workoutTimer?.cancel();
  }

  /// Resume workout
  void resumeWorkout() {
    if (!mounted) return;
    state = state.copyWith(flowStatus: WorkoutFlowStatus.active);
    _startWorkoutTimer();
  }

  /// Reset workout session state
  void resetWorkoutSession() {
    _workoutTimer?.cancel();
    _autoSaveTimer?.cancel();
    if (!mounted) return;
    state = const WorkoutSessionState();
    debugPrint('🔄 Workout session state reset');
  }

  /// Complete workout
  Future<void> completeWorkout({
    int? userRating,
    String? userFeedback,
  }) async {
    if (!mounted) return;
    state = state.copyWith(flowStatus: WorkoutFlowStatus.saving);
    _workoutTimer?.cancel();
    _autoSaveTimer?.cancel();

    final userId = SupabaseService.currentUser?.id;
    if (userId == null || state.currentWorkout == null) {
      if (!mounted) return;
      state = state.copyWith(flowStatus: WorkoutFlowStatus.error, error: 'User or workout not found');
      return;
    }

    try {
      debugPrint('🏁 Starting workout completion process...');
      debugPrint('   Workout: ${state.currentWorkout!.name}');
      debugPrint('   Duration: ${state.elapsedTime.inMinutes} minutes');
      debugPrint('   User ID: $userId');

      // Calculate workout statistics
      final totalSetsCompleted = _countCompletedSets();
      final estimatedCalories = _calculateCaloriesBurned();
      
      debugPrint('📊 Workout Statistics:');
      debugPrint('   Sets Completed: $totalSetsCompleted');
      debugPrint('   Calories Burned: $estimatedCalories');
      debugPrint('   Rating: ${userRating ?? 'Not provided'}');

      // Step 1: Save to completed_workouts table
      debugPrint('📝 Step 1: Saving to completed_workouts table...');
      await SupabaseService.client
          .from('completed_workouts')
          .insert({
            'workout_id': state.currentWorkout!.id,
            'user_id': userId,
            'date_completed': DateTime.now().toIso8601String(),
            'duration': state.elapsedTime.inSeconds,
            'calories_burned': estimatedCalories,
            'rating': userRating,
            'user_feedback_completed_workout': userFeedback,
            'completed_workout_summary': jsonEncode({
              'total_sets_completed': totalSetsCompleted,
              'exercises_completed': state.currentExerciseIndex + 1,
              'session_data': state.sessionData,
              'workout_name': state.currentWorkout!.name,
              'completed_at': DateTime.now().toIso8601String(),
            }),
          });
      debugPrint('✅ Step 1 Complete: Workout saved to completed_workouts table');

      // Step 2: Update workout logs table
      if (state.workoutLogId != null) {
        debugPrint('📝 Step 2: Updating workout logs table...');
        try {
          await SupabaseService.client
              .from('workout_logs')
              .update({
                'status': 'completed',
                'completed_at': DateTime.now().toIso8601String(),
                'ended_at': DateTime.now().toIso8601String(),
                'duration_seconds': state.elapsedTime.inSeconds,
                'rating': userRating,
                'notes': userFeedback,
              })
              .eq('id', state.workoutLogId!);
          debugPrint('✅ Step 2 Complete: Workout log updated');
        } catch (e) {
          debugPrint('❌ Step 2 Warning: Failed to update workout log: $e');
        }
      } else {
        debugPrint('ℹ️ Step 2 Skipped: No workout log ID available');
      }

      // Step 3: Move workout from active to completed status
      debugPrint('📝 Step 3: Marking original workout as completed...');
      await _moveWorkoutToCompleted(state.currentWorkout!.id, userId);
      debugPrint('✅ Step 3 Complete: Original workout marked as completed');

      // Update final state
      if (!mounted) return;
      state = state.copyWith(flowStatus: WorkoutFlowStatus.completed);

      debugPrint('🎉 WORKOUT COMPLETION SUCCESSFUL!');
      debugPrint('   ✅ Workout data saved to completed_workouts table');
      debugPrint('   ✅ Original workout marked as completed in workouts table');
      debugPrint('   ✅ All completed sets preserved in completed_sets table');
      debugPrint('   📈 User statistics will be updated on next dashboard load');

      // Add a small delay to ensure database transaction is fully committed
      await Future.delayed(const Duration(milliseconds: 500));

      // Refresh dashboard data to show next workout immediately using staggered invalidation
      // This prevents rapid successive invalidations that can cause sliver assertion errors
      try {
        Future.delayed(const Duration(milliseconds: 100), () {
          ref.invalidate(todayWorkoutProvider);
        });

        Future.delayed(const Duration(milliseconds: 200), () {
          ref.invalidate(dashboardDataProvider);
          ref.invalidate(userStatsProvider);
        });

        Future.delayed(const Duration(milliseconds: 300), () {
          ref.invalidate(allWorkoutsProvider);
          ref.invalidate(workoutStatisticsProvider);
          ref.invalidate(completedWorkoutsProvider);
        });

        debugPrint('✅ Dashboard providers invalidated with staggered timing - UI will refresh with new data');
      } catch (e) {
        debugPrint('⚠️ Error during provider invalidation: $e');
      }

      // ---- Webhook integration: send workout data ----
      debugPrint('🔗 STARTING WEBHOOK PROCESS');
      debugPrint('   Workout: ${state.currentWorkout!.name}');
      debugPrint('   User: $userId');
      debugPrint('   Feedback: $userFeedback');
      debugPrint('   Completed sets: ${_countCompletedSets()}');
      
      try {
        final payload = _buildWebhookPayload(
          userId: userId,
          feedback: userFeedback,
        );
        
        debugPrint('📦 WEBHOOK PAYLOAD BUILT - Analyzing structure...');
        
        // Log detailed payload analysis
        WebhookService.logPayloadStructure(payload);
        
        debugPrint('🚀 SENDING TO N8N...');
        await WebhookService.sendNextWorkoutPayload(payload, useTestEndpoint: false);
        debugPrint('✅ WEBHOOK SENT SUCCESSFULLY');
      } catch (e) {
        debugPrint('❌ Failed to send workout webhook: $e');
        debugPrint('   Stack trace: ${StackTrace.current}');
      }

    } catch (e) {
      debugPrint('❌ WORKOUT COMPLETION FAILED: $e');
      if (!mounted) return;
      state = state.copyWith(
        flowStatus: WorkoutFlowStatus.error,
        error: 'Failed to complete workout: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// Count completed sets from session data
  int _countCompletedSets() {
    int completedSets = 0;
    for (final entry in state.sessionData.entries) {
      if (entry.key.startsWith('set_') && entry.value is Map) {
        final setData = entry.value as Map<String, dynamic>;
        if (setData['completed'] == true) {
          completedSets++;
        }
      }
    }
    return completedSets;
  }

  /// Calculate calories burned based on workout data
  int _calculateCaloriesBurned() {
    // Basic calculation: 8 calories per minute of workout
    final durationMinutes = state.elapsedTime.inMinutes;
    return (durationMinutes * 8).round();
  }

  /// Cancel workout
  Future<void> cancelWorkout() async {
    if (!mounted) return;
    state = state.copyWith(flowStatus: WorkoutFlowStatus.cancelled);
    _workoutTimer?.cancel();
    _autoSaveTimer?.cancel();

    if (state.workoutLogId != null) {
      try {
        await SupabaseService.client
            .from('workout_logs')
            .update({
              'status': 'cancelled',
              'ended_at': DateTime.now().toIso8601String(),
              'duration_seconds': state.elapsedTime.inSeconds,
            })
            .eq('id', state.workoutLogId!);
        debugPrint('✅ Workout log cancelled in database');
      } catch (e) {
        debugPrint('❌ Failed to update workout log status: $e');
      }
    }
    
    // Reset the workout session state
    resetWorkoutSession();
  }


  /// Complete current set and save to database
  Future<Map<String, dynamic>> completeSet({
    required int performedReps,
    required double performedWeight,
    String? difficulty,
  }) async {
    if (!mounted) return Future.value({});
    if (state.currentWorkout == null) return Future.value({});

    final currentExercise = state.currentWorkout!.exercises[state.currentExerciseIndex];
    final userId = SupabaseService.currentUser?.id;


    debugPrint('🏋️ COMPLETING SET:');
    debugPrint('  Exercise: ${currentExercise.name}');
    debugPrint('  Current Set Index: ${state.currentSetIndex}');
    debugPrint('  Current Set Number: ${state.currentSetIndex + 1}');
    debugPrint('  Total Sets: ${currentExercise.sets}');
    debugPrint('  Completed Sets Before: ${currentExercise.completedSets}');
    debugPrint('  Is Last Set Before: ${currentExercise.isLastSet}');
    debugPrint('  Performed Reps: $performedReps');
    debugPrint('  Performed Weight: $performedWeight');

    try {
      // Check for personal records
      Map<String, dynamic> prInfo = {};
      if (userId != null) {
        prInfo = await _checkPersonalRecords(
          exerciseId: currentExercise.id,
          exerciseName: currentExercise.name,
          performedReps: performedReps,
          performedWeight: performedWeight,
          userId: userId,
        );
      }

      // Validate exercise performance data
      _validateExercisePerformanceData(performedReps, performedWeight, difficulty);

      // Save completed set to database
      await SupabaseService.client
          .from('completed_sets')
          .insert({
            'workout_id': ValidationUtils.sanitizeString(state.currentWorkout!.id),
            'workout_exercise_id': ValidationUtils.sanitizeString(currentExercise.id),
            'performed_set_order': state.currentSetIndex + 1,
            'performed_reps': performedReps,
            'performed_weight': performedWeight.round(),
            'set_feedback_difficulty': difficulty,
          });

      // Update session data with completed set info
      final setKey = 'set_${state.currentExerciseIndex}_${state.currentSetIndex}';
      _updateSessionData({
        setKey: {
          'completed': true,
          'reps': performedReps,
          'weight': performedWeight,
          'difficulty': difficulty,
          'completed_at': DateTime.now().toIso8601String(),
          'pr_info': prInfo,
        }
      });

      // Update the exercise's completed sets count
      final updatedExercises = List<WorkoutExercise>.from(state.currentWorkout!.exercises);
      final exerciseIndex = state.currentExerciseIndex;
      final updatedExercise = WorkoutExercise(
        id: currentExercise.id,
        name: currentExercise.name,
        description: currentExercise.description,
        sets: currentExercise.sets,
        targetReps: currentExercise.targetReps,
        targetWeights: currentExercise.targetWeights,
        videoUrl: currentExercise.videoUrl,
        thumbnailUrl: currentExercise.thumbnailUrl,
        instructions: currentExercise.instructions,
        restInterval: currentExercise.restInterval,
        isCompleted: currentExercise.completedSets + 1 >= currentExercise.sets,
        completedSets: currentExercise.completedSets + 1,
      );
      updatedExercises[exerciseIndex] = updatedExercise;

      // Update the workout with the new exercise data
      final updatedWorkout = WorkoutSession(
        id: state.currentWorkout!.id,
        name: state.currentWorkout!.name,
        description: state.currentWorkout!.description,
        estimatedDuration: state.currentWorkout!.estimatedDuration,
        estimatedCalories: state.currentWorkout!.estimatedCalories,
        totalSets: state.currentWorkout!.totalSets,
        exercises: updatedExercises,
        backgroundImageUrl: state.currentWorkout!.backgroundImageUrl,
        status: state.currentWorkout!.status,
        startedAt: state.currentWorkout!.startedAt,
        completedAt: state.currentWorkout!.completedAt,
        currentExerciseIndex: state.currentWorkout!.currentExerciseIndex,
        currentSetIndex: state.currentWorkout!.currentSetIndex,
        sessionData: state.currentWorkout!.sessionData,
      );

      // Update state with the new workout data
      if (!mounted) return Future.value({});
      state = state.copyWith(currentWorkout: updatedWorkout);

      debugPrint('✅ SET COMPLETED SUCCESSFULLY:');
      debugPrint('  Completed Sets After: ${updatedExercise.completedSets}');
      debugPrint('  Is Last Set After: ${updatedExercise.isLastSet}');
      debugPrint('  Exercise Completed: ${updatedExercise.isCompleted}');

      return prInfo;
    } catch (e) {
      debugPrint('Error saving completed set: $e');
      rethrow;
    }
  }

  /// Move to next set
  void nextSet() {
    if (state.currentWorkout != null) {
      final currentExercise = state.currentWorkout!.exercises[state.currentExerciseIndex];
      debugPrint('🔄 MOVING TO NEXT SET:');
      debugPrint('  Current Set Index: ${state.currentSetIndex}');
      debugPrint('  Total Sets: ${currentExercise.sets}');
      debugPrint('  Completed Sets: ${currentExercise.completedSets}');

      // The current set index should be based on completed sets
      final nextSetIndex = currentExercise.completedSets;
      
      if (nextSetIndex < currentExercise.sets) {
        if (!mounted) return;
        state = state.copyWith(
          currentSetIndex: nextSetIndex,
          currentReps: 0,  // Reset reps for new set
          currentWeight: state.currentWeight, // Keep weight from previous set (user typically uses same weight)
        );
        debugPrint('  New Set Index: ${state.currentSetIndex}');
        debugPrint('  Reset reps to 0, keeping weight at ${state.currentWeight}');
      } else {
        debugPrint('  ⚠️ Cannot move to next set - all sets completed');
      }
    }
  }

  /// Move to next exercise
  void nextExercise() {
    if (state.currentWorkout != null) {
      debugPrint('🔄 MOVING TO NEXT EXERCISE:');
      debugPrint('  Current Exercise Index: ${state.currentExerciseIndex}');
      debugPrint('  Total Exercises: ${state.currentWorkout!.exercises.length}');

      if (state.currentExerciseIndex < state.currentWorkout!.exercises.length - 1) {
        if (!mounted) return;
        state = state.copyWith(
          currentExerciseIndex: state.currentExerciseIndex + 1,
          currentSetIndex: 0, // Reset to first set of new exercise
          currentReps: 0,     // Reset reps for new exercise
          currentWeight: 0.0, // Reset weight for new exercise
        );
        debugPrint('  New Exercise Index: ${state.currentExerciseIndex}');
        debugPrint('  Reset Set Index to: 0, Reps to: 0, Weight to: 0.0');
      } else {
        debugPrint('  ⚠️ Cannot move to next exercise - already at last exercise');
      }
    }
  }

  /// Update session data
  void _updateSessionData(Map<String, dynamic> data) {
    final newSessionData = Map<String, dynamic>.from(state.sessionData);
    newSessionData.addAll(data);
    if (!mounted) return;
    state = state.copyWith(sessionData: newSessionData);
  }

  /// Updates the current rep count.
  void updateCurrentReps(int reps) {
    if (!mounted) return;
    state = state.copyWith(currentReps: reps.clamp(0, 999));
    _updateSessionData({'current_reps': reps});
    debugPrint('🎤 Voice Control: Updated reps to ${state.currentReps}');
  }
  
  /// Updates the current weight being used.
  void updateCurrentWeight(double weight) {
    if (!mounted) return;
    state = state.copyWith(currentWeight: weight.clamp(0, 9999));
    _updateSessionData({'current_weight': weight});
    debugPrint('🎤 Voice Control: Updated weight to ${state.currentWeight} lbs');
  }

  /// Initiates a rest period.
  void startRestPeriod(int duration) {
    if (!mounted) return;
    state = state.copyWith(
      isResting: true,
      restDuration: duration.clamp(0, 3600),
    );
    _updateSessionData({
      'voice_start_rest': true,
      'rest_duration': duration,
    });
    debugPrint('🎤 Voice Control: Started rest for $duration seconds');
  }

  /// Clears the rest state.
  void clearVoiceRest() {
    if (!mounted) return;
    if (state.isResting) {
      state = state.copyWith(isResting: false, restDuration: 0);
       _updateSessionData({
        'voice_start_rest': false,
        'rest_duration': 0,
      });
      debugPrint('🎤 Voice Control: Rest period cleared');
    }
  }

  /// Updates the status of the voice assistant connection.
  void updateVoiceStatus(VoiceStatus status) {
    if (!mounted) return;
    state = state.copyWith(voiceStatus: status);
  }

  /// Check for personal records
  Future<Map<String, dynamic>> _checkPersonalRecords({
    required String exerciseId,
    required String exerciseName,
    required int performedReps,
    required double performedWeight,
    required String userId,
  }) async {
    try {
      // Calculate 1RM using Epley formula: weight * (1 + reps/30)
      final oneRM = performedWeight * (1 + performedReps / 30);
      
      // Get user's previous best for this exercise
      final previousBests = await SupabaseService.client
          .from('completed_sets')
          .select('performed_reps, performed_weight')
          .eq('workout_exercise_id', exerciseId) // Use proper exercise ID
          .order('performed_weight', ascending: false)
          .limit(10);

      Map<String, dynamic> prInfo = {
        'is_pr': false,
        'pr_type': null,
        'improvement': null,
      };

      if (previousBests.isEmpty) {
        // First time doing this exercise
        prInfo = {
          'is_pr': true,
          'pr_type': 'first_time',
          'improvement': 'First time doing $exerciseName!',
        };
      } else {
        // Check for weight PR
        final maxWeight = previousBests.fold<double>(0, (max, set) => 
            (set['performed_weight'] as num).toDouble() > max 
                ? (set['performed_weight'] as num).toDouble() 
                : max);

        // Check for rep PR at same weight
        final sameWeightSets = previousBests.where((set) => 
            (set['performed_weight'] as num).toDouble() == performedWeight);
        
        final maxRepsAtWeight = sameWeightSets.isEmpty ? 0 : 
            sameWeightSets.fold<int>(0, (max, set) => 
                (set['performed_reps'] as int) > max 
                    ? (set['performed_reps'] as int) 
                    : max);

        // Check for 1RM PR
        final best1RM = previousBests.fold<double>(0, (max, set) {
          final weight = (set['performed_weight'] as num).toDouble();
          final reps = (set['performed_reps'] as int);
          final estimated1RM = weight * (1 + reps / 30);
          return estimated1RM > max ? estimated1RM : max;
        });

        if (performedWeight > maxWeight) {
          prInfo = {
            'is_pr': true,
            'pr_type': 'weight',
            'improvement': '+${(performedWeight - maxWeight).toStringAsFixed(1)}lbs',
            'previous_best': maxWeight,
          };
        } else if (performedWeight == maxWeight && performedReps > maxRepsAtWeight) {
          prInfo = {
            'is_pr': true,
            'pr_type': 'reps',
            'improvement': '+${performedReps - maxRepsAtWeight} reps',
            'previous_best': maxRepsAtWeight,
          };
        } else if (oneRM > best1RM) {
          prInfo = {
            'is_pr': true,
            'pr_type': 'estimated_1rm',
            'improvement': '+${(oneRM - best1RM).toStringAsFixed(1)}lbs estimated 1RM',
            'previous_best': best1RM.toStringAsFixed(1),
            'new_1rm': oneRM.toStringAsFixed(1),
          };
        }
      }

      return prInfo;
    } catch (e) {
      debugPrint('Error checking personal records: $e');
      return {'is_pr': false};
    }
  }

  /// Validate exercise performance data before database operations
  void _validateExercisePerformanceData(int performedReps, double performedWeight, String? difficulty) {
    // Validate reps
    if (!ValidationUtils.isValidReps(performedReps)) {
      throw ValidationException('Invalid repetition count. Must be between 0 and 100.', 'performedReps');
    }

    // Validate weight
    if (!ValidationUtils.isValidWeight(performedWeight)) {
      throw ValidationException('Invalid weight. Must be between 0 and 1000 lbs/kg.', 'performedWeight');
    }

    // Validate difficulty rating if provided
    if (difficulty != null) {
      // Convert string difficulty to rating number for validation
      final difficultyRating = _difficultyToRating(difficulty);
      if (!ValidationUtils.isValidRating(difficultyRating)) {
        throw ValidationException('Invalid difficulty rating. Must be between 1 and 5.', 'difficulty');
      }
    }
  }

  /// Convert difficulty string to rating number
  int _difficultyToRating(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'very easy':
        return 1;
      case 'easy':
        return 2;
      case 'moderate':
        return 3;
      case 'hard':
        return 4;
      case 'very hard':
        return 5;
      default:
        return 3; // Default to moderate
    }
  }

  /// Move completed workout from 'workouts' table to completed status and delete from active workouts
  Future<void> _moveWorkoutToCompleted(String workoutId, String userId) async {
    try {
      debugPrint('🔄 Starting workout completion process for: $workoutId');

      // First, validate that the workout belongs to the user and get workout details
      final workoutResponse = await SupabaseService.client
          .from('workouts')
          .select('id, user_id, name, is_completed, is_active')
          .eq('id', workoutId)
          .eq('user_id', userId)
          .maybeSingle();

      if (workoutResponse == null) {
        debugPrint('⚠️ Workout not found or does not belong to user: $workoutId');
        throw Exception('Workout not found or access denied');
      }

      // Check if workout is already completed
      if (workoutResponse['is_completed'] == true) {
        debugPrint('ℹ️ Workout already marked as completed: $workoutId');
        return;
      }

      debugPrint('✅ Workout validated: ${workoutResponse['name']}');
      debugPrint('📝 Marking workout as completed in workouts table...');

      // Mark the workout as completed and inactive in the workouts table
      final updateResult = await SupabaseService.client
          .from('workouts')
          .update({
            'is_completed': true,
            'end_time': DateTime.now().toIso8601String(),
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', workoutId)
          .eq('user_id', userId)
          .select('id, name');

      if (updateResult.isNotEmpty) {
        debugPrint('✅ Workout successfully marked as completed: ${updateResult.first['name']}');
        debugPrint('📊 Workout data preserved in workouts table for historical reference');
        debugPrint('📋 Completed workout details saved to completed_workouts table');
      } else {
        debugPrint('⚠️ No rows updated - workout may have been modified by another process');
      }

      // Verify the completion was successful
      final verificationResponse = await SupabaseService.client
          .from('workouts')
          .select('is_completed, is_active, end_time')
          .eq('id', workoutId)
          .single();

      if (verificationResponse['is_completed'] == true && verificationResponse['is_active'] == false) {
        debugPrint('🎉 Workout completion verified successfully!');
        debugPrint('   - Workout ID: $workoutId');
        debugPrint('   - Completed: ${verificationResponse['is_completed']}');
        debugPrint('   - Active: ${verificationResponse['is_active']}');
        debugPrint('   - End Time: ${verificationResponse['end_time']}');
      } else {
        throw Exception('Workout completion verification failed');
      }

    } catch (e) {
      debugPrint('❌ Error during workout completion process: $e');
      // Re-throw the error since workout completion is critical
      throw Exception('Failed to complete workout transition: ${e.toString()}');
    }
  }

  /// Synchronize current set index with completed sets
  void _syncCurrentSetIndex() {
    if (state.currentWorkout != null && state.currentExerciseIndex < state.currentWorkout!.exercises.length) {
      final currentExercise = state.currentWorkout!.exercises[state.currentExerciseIndex];
      final correctSetIndex = currentExercise.completedSets;
      
      if (state.currentSetIndex != correctSetIndex) {
        debugPrint('🔄 Syncing set index: ${state.currentSetIndex} -> $correctSetIndex');
        if (!mounted) return;
        state = state.copyWith(currentSetIndex: correctSetIndex);
      }
    }
  }

  /// Skip current set and move to next
  Future<void> skipSet() async {
    if (!mounted) return;
    if (state.currentWorkout == null) return;

    final currentExercise = state.currentWorkout!.exercises[state.currentExerciseIndex];

    debugPrint('⏭️ SKIPPING SET:');
    debugPrint('  Exercise: ${currentExercise.name}');
    debugPrint('  Current Set Index: ${state.currentSetIndex}');
    debugPrint('  Current Set Number: ${state.currentSetIndex + 1}');
    debugPrint('  Total Sets: ${currentExercise.sets}');
    debugPrint('  Completed Sets Before: ${currentExercise.completedSets}');

    try {
      // Record skipped set in session data (but not in database)
      final setKey = 'set_${state.currentExerciseIndex}_${state.currentSetIndex}';
      _updateSessionData({
        setKey: {
          'completed': false,
          'skipped': true,
          'skipped_at': DateTime.now().toIso8601String(),
        }
      });

      // Update the exercise's completed sets count (even for skipped sets to maintain proper indexing)
      final updatedExercises = List<WorkoutExercise>.from(state.currentWorkout!.exercises);
      final exerciseIndex = state.currentExerciseIndex;
      final updatedExercise = WorkoutExercise(
        id: currentExercise.id,
        name: currentExercise.name,
        description: currentExercise.description,
        sets: currentExercise.sets,
        targetReps: currentExercise.targetReps,
        targetWeights: currentExercise.targetWeights,
        videoUrl: currentExercise.videoUrl,
        thumbnailUrl: currentExercise.thumbnailUrl,
        instructions: currentExercise.instructions,
        restInterval: currentExercise.restInterval,
        isCompleted: currentExercise.completedSets + 1 >= currentExercise.sets,
        completedSets: currentExercise.completedSets + 1, // Increment even for skipped sets
      );
      updatedExercises[exerciseIndex] = updatedExercise;

      // Update the workout with the new exercise data
      final updatedWorkout = WorkoutSession(
        id: state.currentWorkout!.id,
        name: state.currentWorkout!.name,
        description: state.currentWorkout!.description,
        estimatedDuration: state.currentWorkout!.estimatedDuration,
        estimatedCalories: state.currentWorkout!.estimatedCalories,
        totalSets: state.currentWorkout!.totalSets,
        exercises: updatedExercises,
        backgroundImageUrl: state.currentWorkout!.backgroundImageUrl,
        status: state.currentWorkout!.status,
        startedAt: state.currentWorkout!.startedAt,
        completedAt: state.currentWorkout!.completedAt,
        currentExerciseIndex: state.currentWorkout!.currentExerciseIndex,
        currentSetIndex: state.currentWorkout!.currentSetIndex,
        sessionData: state.currentWorkout!.sessionData,
      );

      // Update state with the new workout data
      if (!mounted) return;
      state = state.copyWith(currentWorkout: updatedWorkout);

      debugPrint('✅ SET SKIPPED SUCCESSFULLY:');
      debugPrint('  Completed Sets After: ${updatedExercise.completedSets}');
      debugPrint('  Is Last Set After: ${updatedExercise.isLastSet}');
      debugPrint('  Exercise Completed: ${updatedExercise.isCompleted}');

    } catch (e) {
      debugPrint('❌ Error skipping set: $e');
      // Continue anyway since skipping shouldn't fail
    }
  }

  /// Build webhook payload
  Map<String, dynamic> _buildWebhookPayload({
    required String userId,
    required String? feedback,
  }) {
    final workout = state.currentWorkout!;

    // Build planned workout list
    final plannedExercises = <Map<String, dynamic>>[];
    for (var i = 0; i < workout.exercises.length; i++) {
      final ex = workout.exercises[i];
      plannedExercises.add({
        'order': i + 1,
        'exercise': ex.name,
        'planned_sets': ex.sets,
        'planned_reps': (ex.targetReps.isNotEmpty ? ex.targetReps.first : 0),
        'planned_weight': (ex.targetWeights.isNotEmpty ? ex.targetWeights.first : 0.0),
        'rest_interval': ex.restInterval,
      });
    }

    // Build actual workout list
    final actualExercises = <Map<String, dynamic>>[];
    for (var i = 0; i < workout.exercises.length; i++) {
      final ex = workout.exercises[i];
      final sets = <Map<String, dynamic>>[];
      for (var s = 0; s < ex.sets; s++) {
        final key = 'set_${i}_${s}';
        final setData = state.sessionData[key] as Map<String, dynamic>?;
        if (setData == null) continue; // skipped/not performed

        final int performedReps = setData['reps'] ?? 0;
        final double performedWeight = (setData['weight'] ?? 0).toDouble();
        final int plannedReps = ex.targetReps.length > s ? ex.targetReps[s] : (ex.targetReps.isNotEmpty ? ex.targetReps.first : 0);

        sets.add({
          'set_order': s + 1,
          'performed_reps': performedReps,
          'performed_weight': performedWeight,
          'rep_difference': plannedReps - performedReps,
          'set_feedback_difficulty': setData['difficulty'],
        });
      }

      actualExercises.add({
        'exercise': ex.name,
        'actual_sets': sets,
      });
    }

    return {
      'user_id': userId,
      'workout_name': workout.name,
      'workout_date': (workout.startedAt ?? DateTime.now()).toUtc().toIso8601String(),
      'planned_workout': {
        'workout_name': workout.name,
        'exercises': plannedExercises,
      },
      'actual_workout': {
        'workout_name': workout.name,
        'exercises': actualExercises,
      },
      'feedback': feedback,
      'additional_metrics': {
        'duration': state.elapsedTime.inMinutes,
        'calories_burned': _calculateCaloriesBurned(),
      }
    };
  }
}

/// Workout session provider
final workoutSessionProvider = StateNotifierProvider<WorkoutSessionNotifier, WorkoutSessionState>((ref) {
  return WorkoutSessionNotifier(ref);
});
