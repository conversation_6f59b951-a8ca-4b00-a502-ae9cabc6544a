import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';
import 'dart:convert';

/// Screen to display workout history
class WorkoutHistoryScreen extends ConsumerStatefulWidget {
  const WorkoutHistoryScreen({super.key});

  @override
  ConsumerState<WorkoutHistoryScreen> createState() => _WorkoutHistoryScreenState();
}

class _WorkoutHistoryScreenState extends ConsumerState<WorkoutHistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 0;
  List<Map<String, dynamic>> _allWorkouts = [];
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent && 
        !_isLoadingMore) {
      _loadMoreWorkouts();
    }
  }

  void _loadMoreWorkouts() async {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });

    try {
      final newWorkouts = await ref.read(paginatedCompletedWorkoutsProvider(_currentPage + 1).future);
      if (newWorkouts.isNotEmpty) {
        setState(() {
          _currentPage++;
          _allWorkouts.addAll(newWorkouts);
        });
      }
    } catch (e) {
      debugPrint('Error loading more workouts: $e');
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final statisticsAsync = ref.watch(workoutStatisticsProvider);
    final initialWorkoutsAsync = ref.watch(paginatedCompletedWorkoutsProvider(0));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Workout History'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Statistics Section
          statisticsAsync.when(
            data: (stats) => _buildStatisticsCard(stats),
            loading: () => _buildLoadingCard(),
            error: (error, stack) => _buildErrorCard(error.toString()),
          ),
          
          const SizedBox(height: 16),
          
          // Workouts List
          Expanded(
            child: initialWorkoutsAsync.when(
              data: (initialWorkouts) {
                if (_allWorkouts.isEmpty && initialWorkouts.isNotEmpty) {
                  _allWorkouts = List.from(initialWorkouts);
                }
                
                if (_allWorkouts.isEmpty) {
                  return _buildEmptyState();
                }
                
                return _buildWorkoutsList();
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorCard(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard(Map<String, dynamic> stats) {
    return Padding(
      padding: AppSpacing.paddingMd,
      child: GlassMorphismCard(
        child: Padding(
          padding: AppSpacing.paddingMd,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your Progress',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Total Workouts',
                      '${stats['total_workouts']}',
                      Icons.fitness_center,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Minutes',
                      '${stats['total_duration_minutes']}',
                      Icons.schedule,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Calories Burned',
                      '${stats['total_calories_burned']}',
                      Icons.local_fire_department,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Current Streak',
                      '${stats['current_streak']} days',
                      Icons.whatshot,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColorPalette.primaryOrange,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTypography.headlineSmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall.copyWith(
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWorkoutsList() {
    return ListView.builder(
      key: const ValueKey('workout_history_list'),
      controller: _scrollController,
      padding: AppSpacing.paddingMd,
      itemCount: _allWorkouts.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _allWorkouts.length) {
          return const Center(
            key: ValueKey('loading_indicator'),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final workout = _allWorkouts[index];
        // Use a unique key for each workout item
        final workoutKey = workout['id']?.toString() ??
                          workout['date_completed']?.toString() ??
                          'workout_$index';
        return Container(
          key: ValueKey('workout_history_$workoutKey'),
          child: _buildWorkoutHistoryCard(workout),
        );
      },
    );
  }

  Widget _buildWorkoutHistoryCard(Map<String, dynamic> workout) {
    final dateCompleted = DateTime.parse(workout['date_completed']);
    final duration = Duration(seconds: workout['duration'] ?? 0);
    final summary = workout['completed_workout_summary'] != null 
        ? jsonDecode(workout['completed_workout_summary'])
        : <String, dynamic>{};

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: GlassMorphismCard(
        child: Padding(
          padding: AppSpacing.paddingMd,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      summary['workout_name'] ?? 'Workout',
                      style: theme.textTheme.titleMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColorPalette.primaryOrange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _formatDate(dateCompleted),
                      style: theme.textTheme.bodySmall.copyWith(
                        color: AppColorPalette.primaryOrange,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildMetric(
                    Icons.schedule,
                    '${duration.inMinutes}m ${duration.inSeconds % 60}s',
                  ),
                  const SizedBox(width: 16),
                  _buildMetric(
                    Icons.local_fire_department,
                    '${workout['calories_burned'] ?? 0} cal',
                  ),
                  const SizedBox(width: 16),
                  if (workout['rating'] != null)
                    _buildMetric(
                      Icons.star,
                      '${workout['rating']}/5',
                    ),
                ],
              ),
              if (summary['total_sets_completed'] != null) ...[
                const SizedBox(height: 8),
                Text(
                  '${summary['total_sets_completed']} sets completed',
                  style: theme.textTheme.bodySmall.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetric(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.white70,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: theme.textTheme.bodySmall.copyWith(
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingCard() {
    return Padding(
      padding: AppSpacing.paddingMd,
      child: GlassMorphismCard(
        child: Container(
          height: 120,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorCard(String error) {
    return Padding(
      padding: AppSpacing.paddingMd,
      child: GlassMorphismCard(
        child: Padding(
          padding: AppSpacing.paddingMd,
          child: Column(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 8),
              Text(
                'Error loading data',
                style: theme.textTheme.titleMedium.copyWith(color: Colors.white),
              ),
              const SizedBox(height: 4),
              Text(
                error,
                style: theme.textTheme.bodySmall.copyWith(color: Colors.white70),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: AppSpacing.paddingMd,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.history,
              size: 64,
              color: Colors.white54,
            ),
            const SizedBox(height: 16),
            Text(
              'No Completed Workouts',
              style: AppTypography.headlineSmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Complete your first workout to see your history here!',
              style: AppTypography.bodyMedium.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '${difference} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}