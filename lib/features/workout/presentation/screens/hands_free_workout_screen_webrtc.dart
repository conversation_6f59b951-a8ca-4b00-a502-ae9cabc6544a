import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/providers/workout_session_provider.dart';
import '../../../../shared/services/openai_realtime_webrtc_service.dart';
import '../../../../core/theme/color_palette.dart';
import '../../domain/models/voice_status.dart';

class HandsFreeWorkoutScreenWebRTC extends ConsumerStatefulWidget {
  final WorkoutSession workout;
  const HandsFreeWorkoutScreenWebRTC({super.key, required this.workout});

  @override
  ConsumerState<HandsFreeWorkoutScreenWebRTC> createState() => _HandsFreeWorkoutScreenWebRTCState();
}

class _HandsFreeWorkoutScreenWebRTCState extends ConsumerState<HandsFreeWorkoutScreenWebRTC>
    with TickerProviderStateMixin {
  
  // WebRTC Service
  late final OpenAIRealtimeWebRTCService _webrtcService;
  
  // Animation controllers
  late AnimationController _pulseController;
  late AnimationController _repCountController;
  late AnimationController _voiceIndicatorController;
  late AnimationController _fadeController;
  late AnimationController _weightChangeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _repCountAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _weightChangeAnimation;
  
  bool _isNavigating = false;
  bool _isDisposed = false;
  bool _isConnecting = false;
  String _connectionStatus = 'Disconnected';
  double _currentWeight = 0.0;
  Map<String, dynamic> _workoutState = {};
  
  @override
  void initState() {
    super.initState();
    
    // Initialize WebRTC service
    _webrtcService = OpenAIRealtimeWebRTCService();
    
    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _repCountController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _voiceIndicatorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );
    
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    
    _weightChangeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _repCountAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _repCountController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
    
    _weightChangeAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _weightChangeController,
      curve: Curves.easeInOut,
    ));
    
    // Start fade in animation
    _fadeController.forward();
    
    // Initialize weight from workout data
    _initializeWeight();
    
    // Setup listeners
    _setupWebRTCListeners();
    
    // Start voice workout after frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startVoiceWorkout();
    });
  }
  
  void _setupWebRTCListeners() {
    // Connection state
    _webrtcService.connectionState.listen((state) {
      if (mounted) {
        setState(() {
          _connectionStatus = state.toString().split('.').last;
        });
        
        // Update voice status based on connection
        if (state == RTCPeerConnectionState.RTCPeerConnectionStateConnected) {
          ref.read(workoutSessionProvider.notifier).updateVoiceStatus(VoiceStatus.connected);
          _voiceIndicatorController.repeat();
        } else if (state == RTCPeerConnectionState.RTCPeerConnectionStateFailed ||
                   state == RTCPeerConnectionState.RTCPeerConnectionStateDisconnected) {
          ref.read(workoutSessionProvider.notifier).updateVoiceStatus(VoiceStatus.disconnected);
          _voiceIndicatorController.stop();
        }
      }
    });
    
    // Workout state updates
    _webrtcService.workoutState.listen((state) {
      if (mounted) {
        setState(() {
          _workoutState = state;
          
          // Check if workout is completed
          if (state['has_workout'] == false && 
              state['completed_sets'] != null && 
              (state['completed_sets'] as List).isNotEmpty) {
            _handleWorkoutCompletion();
          }
        });
        
        // Update UI when sets are logged
        if (state['completed_sets'] != null) {
          final completedSets = state['completed_sets'] as List;
          if (completedSets.isNotEmpty) {
            final lastSet = completedSets.last;
            
            // Just trigger animations - don't sync with workout session provider
            // The WebRTC service already handles database persistence
            _triggerRepAnimation();
            
            // Check for PR
            if (lastSet['is_pr'] == true) {
              _triggerWeightAnimation();
            }
          }
        }
        
        // Don't automatically sync exercise index - let the WebRTC service manage the workout flow
        // The WebRTC service is the source of truth for workout progression
      }
    });
    
    // Error handling
    _webrtcService.errors.listen((error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }
  
  void _initializeWeight() {
    final sessionState = ref.read(workoutSessionProvider);
    
    if (sessionState.currentWeight > 0) {
      _currentWeight = sessionState.currentWeight;
      return;
    }
    
    final workout = sessionState.currentWorkout ?? widget.workout;
    final currentExerciseIndex = sessionState.currentExerciseIndex;
    final currentSetIndex = sessionState.currentSetIndex;
    
    if (currentExerciseIndex < workout.exercises.length) {
      final exercise = workout.exercises[currentExerciseIndex];
      if (exercise.targetWeights.isNotEmpty && currentSetIndex < exercise.targetWeights.length) {
        _currentWeight = exercise.targetWeights[currentSetIndex];
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _currentWeight > 0) {
            ref.read(workoutSessionProvider.notifier).updateCurrentWeight(_currentWeight);
          }
        });
      }
    }
  }
  
  Future<void> _requestPermissions() async {
    final status = await Permission.microphone.request();
    if (!status.isGranted) {
      throw Exception('Microphone permission not granted');
    }
  }
  
  Future<void> _startVoiceWorkout() async {
    if (_isConnecting || _webrtcService.isConnected) return;
    
    setState(() {
      _isConnecting = true;
      _connectionStatus = 'Requesting permissions...';
    });
    
    try {
      // Request microphone permission
      await _requestPermissions();
      
      setState(() {
        _connectionStatus = 'Connecting to AI coach...';
      });
      
      // Connect to OpenAI Realtime API
      await _webrtcService.connect();
      
      // Load the current workout
      await Future.delayed(const Duration(seconds: 1)); // Give connection time to stabilize
      
      // Send command to load the specific workout
      _webrtcService.sendTextMessage('Load workout ${widget.workout.id}');
      
      // Request initial greeting after workout is loaded
      await Future.delayed(const Duration(seconds: 2));
      _webrtcService.sendTextMessage('Start workout and tell me what exercise we\'re doing');
      
      setState(() {
        _isConnecting = false;
      });
      
    } catch (e) {
      setState(() {
        _isConnecting = false;
        _connectionStatus = 'Failed: ${e.toString()}';
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to start voice mode: $e')),
        );
      }
    }
  }
  
  void _stopVoiceWorkout() {
    if (!_isDisposed) {
      try {
        _webrtcService.disconnect();
      } catch (e) {
        debugPrint('Error stopping voice workout: $e');
      }
    }
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    
    // Stop voice workout before disposing
    _stopVoiceWorkout();
    
    // Dispose animation controllers
    _pulseController.dispose();
    _repCountController.dispose();
    _voiceIndicatorController.dispose();
    _fadeController.dispose();
    _weightChangeController.dispose();
    
    // Dispose WebRTC service
    _webrtcService.dispose();
    
    super.dispose();
  }
  
  void _triggerRepAnimation() {
    _repCountController.forward(from: 0.0).then((_) {
      if(mounted) {
        _repCountController.reverse();
      }
    });
  }
  
  void _triggerWeightAnimation() {
    _weightChangeController.forward(from: 0.0).then((_) {
      if(mounted) {
        _weightChangeController.reverse();
      }
    });
  }
  
  void _handleWorkoutCompletion() {
    if (!_isNavigating) {
      _isNavigating = true;
      _stopVoiceWorkout();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        context.pushReplacement(
          '/workout-completion',
          extra: widget.workout,
        );
      });
    }
  }
  
  String _getCurrentExerciseName() {
    // Always use the workout state from WebRTC service as source of truth
    if (_workoutState['has_workout'] != true) {
      return 'Loading workout...';
    }
    
    final exercises = widget.workout.exercises;
    final currentIndex = _workoutState['current_exercise_index'] ?? 0;
    
    // Validate index to prevent out of bounds
    if (currentIndex >= 0 && currentIndex < exercises.length) {
      return exercises[currentIndex].name;
    }
    
    // If we're beyond the last exercise, the workout is complete
    return 'Workout Complete 🎉';
  }
  
  Widget _buildQuickCommand(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: Colors.white.withOpacity(0.6),
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withOpacity(0.6),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  int _getCurrentSet() {
    return (_workoutState['current_set_index'] ?? 0) + 1;
  }
  
  int _getTotalSets() {
    final exercises = widget.workout.exercises;
    final currentIndex = _workoutState['current_exercise_index'] ?? 0;
    
    // Validate index to prevent out of bounds
    if (currentIndex >= 0 && currentIndex < exercises.length) {
      return exercises[currentIndex].sets;
    }
    
    // If we're beyond exercises, return 0
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            // Animated background gradient with parallax effect
            AnimatedBuilder(
              animation: _voiceIndicatorController,
              builder: (context, child) {
                return Container(
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      center: Alignment(
                        math.sin(_voiceIndicatorController.value * math.pi * 2) * 0.2,
                        math.cos(_voiceIndicatorController.value * math.pi * 2) * 0.2,
                      ),
                      radius: 1.5,
                      colors: [
                        AppColorPalette.primaryOrange.withOpacity(0.15),
                        AppColorPalette.darkBackground,
                      ],
                    ),
                  ),
                );
              },
            ),
            
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Enhanced top bar with glass morphism
                  Container(
                    margin: const EdgeInsets.all(16.0),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.white.withOpacity(0.05),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Close button with ripple
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  backgroundColor: AppColorPalette.darkSurface,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  title: const Text(
                                    'End Workout?',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  content: Text(
                                    'Your progress will be saved',
                                    style: TextStyle(
                                      color: AppColorPalette.darkTextSecondary,
                                    ),
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.of(context).pop(),
                                      child: Text(
                                        'Continue',
                                        style: TextStyle(
                                          color: AppColorPalette.primaryOrange,
                                        ),
                                      ),
                                    ),
                                    ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red.shade700,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                      ),
                                      onPressed: () {
                                        _stopVoiceWorkout();
                                        Navigator.of(context).pop();
                                        context.go('/');
                                      },
                                      child: const Text('End Workout'),
                                    ),
                                  ],
                                ),
                              );
                            },
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              child: const Icon(
                                Icons.close_rounded,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ),
                        ),
                        
                        // Animated connection status pill
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            gradient: _webrtcService.isConnected
                              ? LinearGradient(
                                  colors: [
                                    Colors.green.shade600,
                                    Colors.green.shade700,
                                  ],
                                )
                              : LinearGradient(
                                  colors: [
                                    Colors.orange.shade600,
                                    Colors.orange.shade700,
                                  ],
                                ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: _webrtcService.isConnected
                                  ? Colors.green.withOpacity(0.3)
                                  : Colors.orange.withOpacity(0.3),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  _webrtcService.isConnected 
                                    ? Icons.mic_rounded 
                                    : Icons.mic_off_rounded,
                                  key: ValueKey(_webrtcService.isConnected),
                                  size: 18,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _webrtcService.isConnected ? 'AI Active' : _connectionStatus,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Main workout display
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Exercise name with enhanced styling
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 32),
                          child: Column(
                            children: [
                              AnimatedSwitcher(
                                duration: const Duration(milliseconds: 500),
                                switchInCurve: Curves.easeOutCubic,
                                switchOutCurve: Curves.easeInCubic,
                                child: Text(
                                  _getCurrentExerciseName(),
                                  key: ValueKey(_getCurrentExerciseName()),
                                  style: theme.textTheme.headlineLarge!.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 28,
                                    letterSpacing: -0.5,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Exercise progress indicator
                              if (_workoutState['has_workout'] == true)
                                AnimatedOpacity(
                                  opacity: 1.0,
                                  duration: const Duration(milliseconds: 300),
                                  child: Builder(
                                    builder: (context) {
                                      final currentIndex = _workoutState['current_exercise_index'] ?? 0;
                                      final totalExercises = widget.workout.exercises.length;
                                      
                                      // Ensure we don't show invalid progress
                                      if (currentIndex >= totalExercises) {
                                        return Text(
                                          'All $totalExercises exercises completed!',
                                          style: TextStyle(
                                            color: AppColorPalette.primaryOrange,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        );
                                      }
                                      
                                      return Text(
                                        'Exercise ${currentIndex + 1} of $totalExercises',
                                        style: TextStyle(
                                          color: AppColorPalette.primaryOrange,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Enhanced central animated circle with set counter
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            // Outer glow effect
                            AnimatedBuilder(
                              animation: _pulseAnimation,
                              builder: (context, child) {
                                return Container(
                                  width: size.width * 0.65,
                                  height: size.width * 0.65,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColorPalette.primaryOrange.withOpacity(0.2 * _pulseAnimation.value),
                                        blurRadius: 50 * _pulseAnimation.value,
                                        spreadRadius: 20 * _pulseAnimation.value,
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                            
                            // Main circle with gradient
                            ScaleTransition(
                              scale: _pulseAnimation,
                              child: Container(
                                width: size.width * 0.6,
                                height: size.width * 0.6,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppColorPalette.primaryOrange,
                                      AppColorPalette.primaryOrange.withRed(255),
                                    ],
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColorPalette.primaryOrange.withOpacity(0.4),
                                      blurRadius: 30,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    // Enhanced voice indicator animation
                                    if (_webrtcService.isConnected)
                                      AnimatedBuilder(
                                        animation: _voiceIndicatorController,
                                        builder: (context, child) {
                                          return Stack(
                                            children: [
                                              // Inner circle
                                              Container(
                                                width: size.width * 0.5,
                                                height: size.width * 0.5,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color: Colors.white.withOpacity(0.2),
                                                    width: 2,
                                                  ),
                                                ),
                                              ),
                                              // Voice waves
                                              Positioned.fill(
                                                child: CustomPaint(
                                                  painter: VoiceWavePainter(
                                                    animation: _voiceIndicatorController,
                                                    color: Colors.white.withOpacity(0.3),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    
                                    // Set counter with enhanced styling
                                    Builder(
                                      builder: (context) {
                                        final currentIndex = _workoutState['current_exercise_index'] ?? 0;
                                        final exercises = widget.workout.exercises;
                                        
                                        // Check if workout is complete
                                        if (currentIndex >= exercises.length) {
                                          return Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.check_circle_outline,
                                                color: Colors.white,
                                                size: 80,
                                              ),
                                              const SizedBox(height: 16),
                                              Text(
                                                'DONE',
                                                style: theme.textTheme.headlineMedium!.copyWith(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                  letterSpacing: 2,
                                                ),
                                              ),
                                            ],
                                          );
                                        }
                                        
                                        // Normal set counter
                                        return Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 16,
                                                vertical: 4,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.black.withOpacity(0.2),
                                                borderRadius: BorderRadius.circular(20),
                                              ),
                                              child: Text(
                                                'SET',
                                                style: theme.textTheme.titleMedium!.copyWith(
                                                  color: Colors.white.withOpacity(0.9),
                                                  letterSpacing: 3,
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            ScaleTransition(
                                              scale: _repCountAnimation,
                                              child: Text(
                                                '${_getCurrentSet()}/${_getTotalSets()}',
                                                style: theme.textTheme.displayLarge!.copyWith(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 72,
                                                  height: 1,
                                                  shadows: [
                                                    Shadow(
                                                      color: Colors.black.withOpacity(0.2),
                                                      offset: const Offset(0, 4),
                                                      blurRadius: 10,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Status indicators with enhanced animations
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 400),
                          switchInCurve: Curves.easeOutBack,
                          switchOutCurve: Curves.easeInBack,
                          transitionBuilder: (Widget child, Animation<double> animation) {
                            return FadeTransition(
                              opacity: animation,
                              child: ScaleTransition(
                                scale: animation,
                                child: child,
                              ),
                            );
                          },
                          child: Column(
                            key: ValueKey('status_${_workoutState['is_resting']}_${_workoutState['completed_sets']?.length ?? 0}'),
                            children: [
                              // Enhanced rest timer with glass morphism
                              if (_workoutState['is_resting'] == true)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.white.withOpacity(0.15),
                                        Colors.white.withOpacity(0.05),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(40),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.2),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 20,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.timer_rounded,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'REST TIME',
                                            style: TextStyle(
                                              color: Colors.white.withOpacity(0.7),
                                              fontSize: 11,
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 1,
                                            ),
                                          ),
                                          Text(
                                            '${_workoutState['rest_time_remaining']}s',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                              height: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              
                              // Enhanced PR indicator with celebration animation
                              if (_workoutState['completed_sets'] != null && 
                                  (_workoutState['completed_sets'] as List).isNotEmpty &&
                                  (_workoutState['completed_sets'] as List).last['is_pr'] == true)
                                Container(
                                  margin: const EdgeInsets.only(top: 20),
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // Glow effect
                                      Container(
                                        width: 280,
                                        height: 60,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(40),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.amber.withOpacity(0.5),
                                              blurRadius: 30,
                                              spreadRadius: 5,
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Main PR container
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14),
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Colors.amber.shade600,
                                              Colors.amber.shade700,
                                            ],
                                          ),
                                          borderRadius: BorderRadius.circular(40),
                                          border: Border.all(
                                            color: Colors.amber.shade400,
                                            width: 2,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            const Icon(
                                              Icons.emoji_events_rounded,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                            const SizedBox(width: 10),
                                            const Text(
                                              'PERSONAL RECORD!',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              
                              // Voice status indicator when not resting
                              if (_workoutState['is_resting'] != true && _webrtcService.isConnected)
                                Container(
                                  margin: const EdgeInsets.only(top: 10),
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.05),
                                    borderRadius: BorderRadius.circular(25),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.1),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: Colors.green,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.green.withOpacity(0.5),
                                              blurRadius: 8,
                                              spreadRadius: 2,
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'AI Coach Listening',
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.8),
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Enhanced bottom instruction section
                  Container(
                    margin: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Paused state indicator
                        if (_workoutState['is_paused'] == true)
                          Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.orange.withOpacity(0.25),
                                  Colors.orange.withOpacity(0.15),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(
                                color: Colors.orange.withOpacity(0.5),
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withOpacity(0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.pause_circle_filled,
                                  color: Colors.orange,
                                  size: 22,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  'PAUSED',
                                  style: theme.textTheme.titleSmall!.copyWith(
                                    color: Colors.orange,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '• Say "Resume"',
                                  style: TextStyle(
                                    color: Colors.orange.withOpacity(0.8),
                                    fontSize: 13,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        
                        // Primary command hint with glass morphism
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withOpacity(0.1),
                                Colors.white.withOpacity(0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.1),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColorPalette.primaryOrange.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Text(
                                      '💬 SAY',
                                      style: TextStyle(
                                        color: AppColorPalette.primaryOrange,
                                        fontSize: 11,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Text(
                                    '"Log set: X reps at Y pounds"',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              // Quick commands in a grid
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildQuickCommand('Skip exercise', Icons.skip_next_rounded),
                                  const SizedBox(width: 8),
                                  _buildQuickCommand('Add rest', Icons.add_circle_outline),
                                  const SizedBox(width: 8),
                                  _buildQuickCommand('Replace', Icons.swap_horiz_rounded),
                                ],
                              ),
                            ],
                          ),
                        ),
                        
                        // Additional commands hint
                        Container(
                          margin: const EdgeInsets.only(top: 12),
                          child: Text(
                            'More: "What\'s next?" • "Pause" • "Previous exercise"',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.4),
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Enhanced loading overlay with blur effect
            if (_isConnecting)
              Container(
                color: Colors.black.withOpacity(0.8),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: AppColorPalette.darkSurface,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: AppColorPalette.primaryOrange.withOpacity(0.2),
                          blurRadius: 40,
                          spreadRadius: 10,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            // Glow effect
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    AppColorPalette.primaryOrange.withOpacity(0.3),
                                    AppColorPalette.primaryOrange.withOpacity(0),
                                  ],
                                ),
                              ),
                            ),
                            // Progress indicator
                            const SizedBox(
                              width: 60,
                              height: 60,
                              child: CircularProgressIndicator(
                                strokeWidth: 3,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColorPalette.primaryOrange,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'Connecting to AI Coach',
                          style: theme.textTheme.titleMedium!.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _connectionStatus,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Enhanced voice wave painter for visual feedback
class VoiceWavePainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;

  VoiceWavePainter({required this.animation, required this.color}) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw multiple concentric waves with different properties
    for (int i = 0; i < 4; i++) {
      final paint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0 - (i * 0.3);

      // Calculate animated properties
      final phase = (animation.value + (i * 0.25)) % 1.0;
      final waveRadius = radius * (0.6 + (i * 0.15) + (phase * 0.4));
      final opacity = (1.0 - phase) * (1.0 - (i * 0.2));
      
      // Add gradient effect to waves
      if (i == 0) {
        // Inner wave with stronger opacity
        paint.shader = RadialGradient(
          colors: [
            color.withOpacity(opacity.clamp(0.0, 0.8)),
            color.withOpacity((opacity * 0.5).clamp(0.0, 0.4)),
          ],
        ).createShader(Rect.fromCircle(center: center, radius: waveRadius));
      } else {
        paint.color = color.withOpacity(opacity.clamp(0.0, 0.6));
      }
      
      // Draw wave
      canvas.drawCircle(center, waveRadius, paint);
      
      // Add subtle glow effect for the innermost wave
      if (i == 0 && phase < 0.5) {
        final glowPaint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 4.0
          ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 10)
          ..color = color.withOpacity((opacity * 0.3).clamp(0.0, 0.3));
        
        canvas.drawCircle(center, waveRadius, glowPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}