import 'package:flutter/material.dart';

/// Typography system for the fitness app
/// Defines consistent text styles across light and dark themes
class AppTypography {
  // Font families
  static const String primaryFontFamily = 'SF Pro Display';
  static const String secondaryFontFamily = 'SF Pro Text';
  static const String monospaceFontFamily = 'SF Mono';
  static const String condensedFontFamily = 'SF Pro Display Condensed';

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;
  
  // Letter spacing values
  static const double tightLetterSpacing = -0.5;
  static const double normalLetterSpacing = 0.0;
  static const double wideLetterSpacing = 0.5;
  
  // Line height multipliers
  static const double tightLineHeight = 1.2;
  static const double normalLineHeight = 1.4;
  static const double relaxedLineHeight = 1.6;
  
  /// Light theme text styles
  static TextTheme lightTextTheme = const TextTheme(
    // Display styles - for large, prominent text
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: extraBold,
      letterSpacing: tightLetterSpacing,
      height: tightLineHeight,
      color: Color(0xFF171717), // grey900
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: tightLineHeight,
      color: Color(0xFF171717),
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: bold,
      letterSpacing: normalLetterSpacing,
      height: tightLineHeight,
      color: Color(0xFF171717),
    ),
    
    // Headline styles - for section headers
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    
    // Title styles - for card titles and important text
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    
    // Body styles - for main content
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: regular,
      letterSpacing: wideLetterSpacing,
      height: relaxedLineHeight,
      color: Color(0xFF171717),
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: regular,
      letterSpacing: wideLetterSpacing,
      height: relaxedLineHeight,
      color: Color(0xFF525252), // grey600
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: regular,
      letterSpacing: wideLetterSpacing,
      height: relaxedLineHeight,
      color: Color(0xFF737373), // grey500
    ),
    
    // Label styles - for buttons and small text
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF171717),
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF525252),
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFF737373),
    ),
  );
  
  /// Dark theme text styles
  static TextTheme darkTextTheme = const TextTheme(
    // Display styles
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: extraBold,
      letterSpacing: tightLetterSpacing,
      height: tightLineHeight,
      color: Color(0xFFF5F5F5), // grey100
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: tightLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: bold,
      letterSpacing: normalLetterSpacing,
      height: tightLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    
    // Headline styles
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    
    // Title styles
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    
    // Body styles
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: regular,
      letterSpacing: wideLetterSpacing,
      height: relaxedLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: regular,
      letterSpacing: wideLetterSpacing,
      height: relaxedLineHeight,
      color: Color(0xFFD4D4D4), // grey300
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: regular,
      letterSpacing: wideLetterSpacing,
      height: relaxedLineHeight,
      color: Color(0xFFA3A3A3), // grey400
    ),
    
    // Label styles
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFF5F5F5),
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFD4D4D4),
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: medium,
      letterSpacing: wideLetterSpacing,
      height: normalLineHeight,
      color: Color(0xFFA3A3A3),
    ),
  );

  // Specialized text styles for fitness app

  /// Tabular figures for numerical displays with consistent alignment
  static TextStyle tabularFigures({
    required double fontSize,
    required FontWeight fontWeight,
    required Color color,
    bool isDark = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: monospaceFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: 0.5,
      height: 1.0,
    );
  }

  /// Extra-bold condensed fonts for display numbers
  static TextStyle displayNumbers({
    required double fontSize,
    required Color color,
    bool isDark = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: black,
      color: color,
      fontFamily: condensedFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: -1.0,
      height: 0.9,
    );
  }

  /// Monospace fonts for timers and countdowns
  static TextStyle timerText({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = medium,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: monospaceFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: 1.0,
      height: 1.0,
    );
  }

  /// High contrast text styles for accessibility
  static TextStyle highContrastText({
    required double fontSize,
    required FontWeight fontWeight,
    required Color color,
    double? letterSpacing,
    double? height,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: primaryFontFamily,
      letterSpacing: letterSpacing ?? normalLetterSpacing,
      height: height ?? normalLineHeight,
      shadows: [
        Shadow(
          offset: const Offset(0, 1),
          blurRadius: 2,
          color: color.withValues(alpha: 0.3),
        ),
      ],
    );
  }

  /// Animated number text style for counting effects
  static TextStyle animatedNumbers({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = extraBold,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: condensedFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: -0.5,
      height: 1.0,
    );
  }

  /// Button text style for consistent button typography
  static const TextStyle buttonTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: semiBold,
    letterSpacing: wideLetterSpacing,
    height: normalLineHeight,
    fontFamily: primaryFontFamily,
  );
}
