import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'color_palette.dart';
import 'typography.dart';
import 'spacing.dart';

/// Global theme configuration for centralized styling across iOS/Android and light/dark themes
class GlobalThemeConfig {
  // Private constructor to prevent instantiation
  GlobalThemeConfig._();

  /// Current platform
  static bool get isIOS => Platform.isIOS;
  static bool get isAndroid => Platform.isAndroid;

  /// Global design tokens that work across all platforms and themes
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 24.0;

  /// Global elevation levels
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;

  /// Global animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  /// Platform-specific adjustments
  static EdgeInsets get platformPadding {
    if (isIOS) {
      return const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
    }
  }

  static double get platformButtonHeight {
    return isIOS ? 50.0 : 48.0;
  }

  static BorderRadius get platformBorderRadius {
    if (isIOS) {
      return BorderRadius.circular(borderRadiusMedium);
    } else {
      return BorderRadius.circular(borderRadiusSmall);
    }
  }

  /// Global button styles that adapt to platform and theme
  static ButtonStyle primaryButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return ElevatedButton.styleFrom(
      backgroundColor: AppColorPalette.primaryOrange,
      foregroundColor: Colors.white,
      elevation: isIOS ? elevationLow : elevationMedium,
      padding: platformPadding,
      minimumSize: Size(double.infinity, platformButtonHeight),
      shape: RoundedRectangleBorder(
        borderRadius: platformBorderRadius,
      ),
      textStyle: theme.textTheme.labelLarge,
    );
  }

  static ButtonStyle secondaryButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return OutlinedButton.styleFrom(
      foregroundColor: isDark ? Colors.white : AppColorPalette.primaryOrange,
      side: BorderSide(
        color: isDark ? Colors.white24 : AppColorPalette.primaryOrange,
        width: 1.5,
      ),
      padding: platformPadding,
      minimumSize: Size(double.infinity, platformButtonHeight),
      shape: RoundedRectangleBorder(
        borderRadius: platformBorderRadius,
      ),
      textStyle: theme.textTheme.labelLarge,
    );
  }

  /// Global card styles
  static BoxDecoration cardDecoration(BuildContext context, {
    double? elevation,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return BoxDecoration(
      color: backgroundColor ?? (isDark ? AppColorPalette.elevation1 : Colors.white),
      borderRadius: borderRadius ?? BorderRadius.circular(borderRadiusMedium),
      boxShadow: elevation != null && elevation > 0 ? [
        BoxShadow(
          color: isDark 
            ? Colors.black.withOpacity(0.3)
            : Colors.black.withOpacity(0.1),
          blurRadius: elevation * 2,
          offset: Offset(0, elevation / 2),
        ),
      ] : null,
      border: isDark ? Border.all(
        color: Colors.white.withOpacity(0.1),
        width: 0.5,
      ) : null,
    );
  }

  /// Global input decoration
  static InputDecoration inputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: isDark 
        ? AppColorPalette.elevation1.withOpacity(0.5)
        : AppColorPalette.grey100,
      border: OutlineInputBorder(
        borderRadius: platformBorderRadius,
        borderSide: BorderSide(
          color: isDark ? Colors.white24 : AppColorPalette.grey300,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: platformBorderRadius,
        borderSide: BorderSide(
          color: isDark ? Colors.white24 : AppColorPalette.grey300,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: platformBorderRadius,
        borderSide: BorderSide(
          color: AppColorPalette.primaryOrange,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: platformBorderRadius,
        borderSide: BorderSide(
          color: AppColorPalette.error,
          width: 1.5,
        ),
      ),
      contentPadding: platformPadding,
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        color: isDark ? Colors.white70 : AppColorPalette.grey600,
      ),
      hintStyle: theme.textTheme.bodyMedium?.copyWith(
        color: isDark ? Colors.white54 : AppColorPalette.grey500,
      ),
    );
  }

  /// Global app bar style
  static AppBarTheme appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return AppBarTheme(
      backgroundColor: isDark ? AppColorPalette.darkBackground : Colors.white,
      foregroundColor: isDark ? Colors.white : AppColorPalette.grey900,
      elevation: isIOS ? 0 : elevationLow,
      centerTitle: isIOS,
      titleTextStyle: theme.textTheme.headlineSmall?.copyWith(
        color: isDark ? Colors.white : AppColorPalette.grey900,
        fontWeight: FontWeight.w600,
      ),
      systemOverlayStyle: isDark 
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark,
    );
  }

  /// Global loading indicator
  static Widget loadingIndicator({
    Color? color,
    double size = 24.0,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.5,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColorPalette.primaryOrange,
        ),
      ),
    );
  }

  /// Global snackbar style
  static SnackBar styledSnackBar(
    String message, {
    SnackBarType type = SnackBarType.info,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? action,
    String? actionLabel,
  }) {
    Color backgroundColor;
    Color textColor = Colors.white;
    IconData icon;

    switch (type) {
      case SnackBarType.success:
        backgroundColor = AppColorPalette.successGreen;
        icon = Icons.check_circle_outline;
        break;
      case SnackBarType.error:
        backgroundColor = AppColorPalette.error;
        icon = Icons.error_outline;
        break;
      case SnackBarType.warning:
        backgroundColor = AppColorPalette.warning;
        icon = Icons.warning_outlined;
        break;
      case SnackBarType.info:
      default:
        backgroundColor = AppColorPalette.primaryOrange;
        icon = Icons.info_outline;
        break;
    }

    return SnackBar(
      content: Row(
        children: [
          Icon(icon, color: textColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: textColor,
              ),
            ),
          ),
        ],
      ),
      backgroundColor: backgroundColor,
      duration: duration,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusSmall),
      ),
      action: action != null && actionLabel != null
        ? SnackBarAction(
            label: actionLabel,
            textColor: textColor,
            onPressed: action,
          )
        : null,
    );
  }
}

/// Snackbar types for consistent messaging
enum SnackBarType {
  success,
  error,
  warning,
  info,
}
