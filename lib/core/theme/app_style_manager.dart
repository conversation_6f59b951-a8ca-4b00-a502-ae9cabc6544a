import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'global_theme_config.dart';
import 'color_palette.dart';
import 'typography.dart';
import '../../shared/widgets/loading/app_loading_components.dart';

/// Centralized style manager for the entire app
/// This is your ONE PLACE to modify app styles globally for both dark/light themes and iOS/Android
class AppStyleManager {
  AppStyleManager._();

  /// Get the current theme-aware style manager instance
  static AppStyleManager of(BuildContext context, WidgetRef ref) {
    return AppStyleManager._();
  }

  // ==================== COLORS ====================
  
  /// Primary brand colors that adapt to theme
  static Color primaryColor(BuildContext context) {
    return AppColorPalette.primaryOrange;
  }

  static Color primaryColorLight(BuildContext context) {
    return AppColorPalette.primaryOrangeLight;
  }

  static Color primaryColorDark(BuildContext context) {
    return AppColorPalette.primaryOrangeDark;
  }

  /// Background colors that adapt to theme
  static Color backgroundColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColorPalette.darkBackground : Colors.white;
  }

  static Color surfaceColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColorPalette.elevation1 : AppColorPalette.grey50;
  }

  static Color cardColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColorPalette.elevation1 : Colors.white;
  }

  /// Text colors that adapt to theme
  static Color textPrimary(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? Colors.white : AppColorPalette.grey900;
  }

  static Color textSecondary(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? Colors.white70 : AppColorPalette.grey600;
  }

  static Color textTertiary(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? Colors.white54 : AppColorPalette.grey500;
  }

  // ==================== BUTTONS ====================
  
  /// Primary button style - use this for main actions
  static ButtonStyle primaryButton(BuildContext context) {
    return GlobalThemeConfig.primaryButtonStyle(context);
  }

  /// Secondary button style - use this for secondary actions
  static ButtonStyle secondaryButton(BuildContext context) {
    return GlobalThemeConfig.secondaryButtonStyle(context);
  }

  /// Danger button style - use this for destructive actions
  static ButtonStyle dangerButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: AppColorPalette.error,
      foregroundColor: Colors.white,
      elevation: GlobalThemeConfig.isIOS ? GlobalThemeConfig.elevationLow : GlobalThemeConfig.elevationMedium,
      padding: GlobalThemeConfig.platformPadding,
      minimumSize: Size(double.infinity, GlobalThemeConfig.platformButtonHeight),
      shape: RoundedRectangleBorder(
        borderRadius: GlobalThemeConfig.platformBorderRadius,
      ),
      textStyle: AppTypography.buttonTextStyle,
    );
  }

  /// Text button style - use this for subtle actions
  static ButtonStyle textButton(BuildContext context) {
    return TextButton.styleFrom(
      foregroundColor: primaryColor(context),
      padding: GlobalThemeConfig.platformPadding,
      textStyle: AppTypography.buttonTextStyle,
    );
  }

  // ==================== INPUTS ====================
  
  /// Standard input decoration - use this for all text fields
  static InputDecoration inputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) {
    return GlobalThemeConfig.inputDecoration(
      context,
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      isError: isError,
    );
  }

  // ==================== CARDS & CONTAINERS ====================
  
  /// Standard card decoration - use this for all cards
  static BoxDecoration cardDecoration(BuildContext context, {
    double? elevation,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return GlobalThemeConfig.cardDecoration(
      context,
      elevation: elevation,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
    );
  }

  /// Elevated card decoration - use this for important cards
  static BoxDecoration elevatedCardDecoration(BuildContext context) {
    return cardDecoration(context, elevation: GlobalThemeConfig.elevationMedium);
  }

  /// Workout card decoration - specific styling for workout cards
  static BoxDecoration workoutCardDecoration(BuildContext context) {
    return cardDecoration(context).copyWith(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          cardColor(context),
          cardColor(context).withValues(alpha: 0.8),
        ],
      ),
    );
  }

  // ==================== LOADING COMPONENTS ====================
  
  /// Primary loading indicator - use this for main loading states
  static Widget primaryLoader({
    double size = 32.0,
    String? message,
  }) {
    return AppLoadingComponents.primaryLoader(
      size: size,
      message: message,
    );
  }

  /// Button loading indicator - use this inside buttons
  static Widget buttonLoader({Color? color}) {
    return AppLoadingComponents.buttonLoader(color: color);
  }

  /// Workout card skeleton - use this while loading workout lists
  static Widget workoutCardSkeleton() {
    return AppLoadingComponents.workoutCardSkeleton();
  }

  /// Exercise list skeleton - use this while loading exercise lists
  static Widget exerciseListSkeleton({int itemCount = 3}) {
    return AppLoadingComponents.exerciseListSkeleton(itemCount: itemCount);
  }

  /// Full screen loader - use this for full screen loading states
  static Widget fullScreenLoader({
    String? message,
    bool canDismiss = false,
  }) {
    return AppLoadingComponents.fullScreenLoader(
      message: message,
      canDismiss: canDismiss,
    );
  }

  // ==================== SNACKBARS & NOTIFICATIONS ====================
  
  /// Success snackbar - use this for success messages
  static SnackBar successSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.success,
      action: action,
      actionLabel: actionLabel,
    );
  }

  /// Error snackbar - use this for error messages
  static SnackBar errorSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.error,
      action: action,
      actionLabel: actionLabel,
    );
  }

  /// Warning snackbar - use this for warning messages
  static SnackBar warningSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.warning,
      action: action,
      actionLabel: actionLabel,
    );
  }

  /// Info snackbar - use this for info messages
  static SnackBar infoSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.info,
      action: action,
      actionLabel: actionLabel,
    );
  }

  // ==================== SPACING & LAYOUT ====================
  
  /// Standard padding - use this for consistent spacing
  static EdgeInsets get standardPadding => GlobalThemeConfig.platformPadding;

  /// Small padding - use this for tight spacing
  static EdgeInsets get smallPadding => const EdgeInsets.all(8.0);

  /// Large padding - use this for loose spacing
  static EdgeInsets get largePadding => const EdgeInsets.all(24.0);

  /// Standard border radius - use this for consistent rounded corners
  static BorderRadius get standardBorderRadius => GlobalThemeConfig.platformBorderRadius;

  /// Large border radius - use this for more rounded corners
  static BorderRadius get largeBorderRadius => BorderRadius.circular(GlobalThemeConfig.borderRadiusLarge);

  // ==================== ANIMATIONS ====================
  
  /// Fast animation duration - use this for quick transitions
  static Duration get fastAnimation => GlobalThemeConfig.animationFast;

  /// Medium animation duration - use this for standard transitions
  static Duration get mediumAnimation => GlobalThemeConfig.animationMedium;

  /// Slow animation duration - use this for dramatic transitions
  static Duration get slowAnimation => GlobalThemeConfig.animationSlow;

  // ==================== HELPER METHODS ====================
  
  /// Check if current theme is dark
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Check if current platform is iOS
  static bool get isIOS => GlobalThemeConfig.isIOS;

  /// Check if current platform is Android
  static bool get isAndroid => GlobalThemeConfig.isAndroid;

  /// Show styled snackbar with context
  static void showSnackBar(BuildContext context, SnackBar snackBar) {
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// Show success message
  static void showSuccess(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, successSnackBar(message, action: action, actionLabel: actionLabel));
  }

  /// Show error message
  static void showError(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, errorSnackBar(message, action: action, actionLabel: actionLabel));
  }

  /// Show warning message
  static void showWarning(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, warningSnackBar(message, action: action, actionLabel: actionLabel));
  }

  /// Show info message
  static void showInfo(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, infoSnackBar(message, action: action, actionLabel: actionLabel));
  }
}

/// Extension to make AppStyleManager easily accessible
extension AppStyleManagerExtension on BuildContext {
  /// Quick access to AppStyleManager
  AppStyleManager get styles => AppStyleManager.of(this, ProviderScope.containerOf(this) as WidgetRef);
}
